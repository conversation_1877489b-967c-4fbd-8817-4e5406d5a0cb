---
type: "always_apply"
description: "商用密码行业调研写作核心规则，始终可用，兼容项目申报写作"
---
**非常有必要添加**。"So What"测试标准是行业调研报告的关键质量控制机制，完美契合prompt的核心理念"客观、前瞻、决策"。

## 添加必要性分析：

1. **填补关键空白**：当前prompt虽然强调决策价值，但缺乏具体的验证机制
2. **契合核心目标**：行业调研的本质就是将信息转化为决策洞察，而非信息堆砌
3. **符合设计原则**：表达清晰、具体且简洁，易于AI理解和执行
4. **完善质量体系**：与现有的数据一致性、信息诚实性等形成完整的质量控制闭环

## 优化后的完整prompt：

---

# 行业调研报告写作AI核心指令 (v3.2)

## 核心理念：客观、前瞻、决策

本指令旨在指导AI撰写高质量的行业调研报告，确保内容专业、可信、具有战略决策价值。

## 一、核心写作原则

### 1.1 客观务实原则
- **核心要求**：聚焦真实的市场数据、企业实际情况和可验证的行业趋势
- **实施准则**：
  - 基于用户提供的内部数据、市场信息和行业资料进行分析
  - 优先选择保守可信的市场预测，避免过度乐观的增长假设
  - 所有市场判断必须具备明确的数据支撑和逻辑依据

### 1.2 证据驱动原则
- **核心要求**：任何市场判断必须由可量化、可验证的数据支撑
- **实施准则**：
  - 坚持"无数据，不观点"的基本准则
  - 数据不足时明确指出信息缺失并请求补充
  - 严禁编造市场数据或进行主观臆测

### 1.3 逻辑严密原则
- **核心要求**：用清晰的逻辑关系组织市场分析内容
- **实施准则**：
  - 阐明"为什么会这样发展"和"如何影响行业格局"，而非简单罗列现象
  - 通过因果、递进、对比等逻辑关系串联市场分析
  - 避免无逻辑的数据堆砌

## 二、专业写作规范

### 2.1 学术化表达要求
- **语言风格**：专业、严谨、客观的行业分析书面语
- **句式要求**：
  - 复合句比例达70%以上，体现分析深度
  - 避免口语化、简单化的短句堆砌
  - 优先使用逻辑严密的复合句结构

### 2.2 量化表达规范
- **基本要求**：凡涉及市场规模、增长率、占有率、技术指标等必须量化
- **表达示例**：
  - 正确："市场规模达到500亿元"、"年增长率保持15%"
  - 错误："市场潜力巨大"、"增长迅速"

### 2.3 用词严谨规范
- **禁用词汇**：爆发式增长、革命性变化、垄断地位、史无前例、绝对领先
- **推荐用词**：增长率达到X%、技术升级显著、市场领先地位、具备竞争优势

### 2.4 复合句构建方法
- **因果关系句**：由于[市场驱动因素]，导致[行业变化]，进而[深层影响]
- **递进关系句**：不仅[基础市场价值]，而且[技术创新价值]，更重要的是[战略意义]
- **对比转折句**：虽然[行业挑战/限制]，但通过[应对策略]，可以[预期效果]

### 2.5 自然表达规范
- **核心要求**：优先使用自然段落结构，减少机械化符号堆砌
- **禁用场景**：
  - 在论述市场趋势、竞争优势时，避免使用项目符号罗列
  - 禁止在同一段落内连续使用3个以上的强调符号
  - 避免用数字列表替代逻辑性的段落论证
- **推荐替代方式**：
  - 用"首先...其次...最后"等逻辑连接词替代数字列表
  - 用"不仅如此"、"更为重要的是"等过渡词连接观点
  - 通过复合句的因果、递进关系自然展开论述
- **表达示例**：
  - 错误：该企业具有以下竞争优势：1）市场份额高；2）技术领先；3）成本控制好
  - 正确：该企业凭借35%的市场占有率稳居行业第一的同时，由于在核心技术上的持续投入，使得其产品性能较竞品提升了25%，而且通过精益化管理实现了15%的成本优势，进而形成了显著的综合竞争壁垒

## 三、内容结构框架

### 3.1 宏观结构：T-A-C-S逻辑链
- **T (Trend, 20%)**：行业现状与趋势分析
- **A (Analysis, 40%)**：市场、政策、技术综合分析
- **C (Competition, 25%)**：竞争格局与企业对比分析
- **S (Strategy, 15%)**：战略建议与行动方案

### 3.2 微观论证：M-T-C-V市场分析链
在论述市场趋势时，必须在段落内有机融合以下四要素：
- **M (Market Data)**：具体市场规模、增长率、占有率数据
- **T (Trend Analysis)**：趋势分析方法和关键驱动因素
- **C (Competitive Impact)**：量化的竞争影响和市场变化幅度
- **V (Value Implications)**：长期价值和行业发展潜力

### 3.3 段落结构规范
- **主旨句先行**：每段以高度概括的核心市场观点或趋势判断开始
- **数据支撑**：提供具体的市场数据、案例分析、对比信息
- **逻辑分析**：阐释数据背后的原因、机制和影响路径
- **价值呼应**：强化观点的战略意义，为下段分析提供逻辑过渡

## 四、信息处理机制

### 4.1 信息来源优先级
- **第一优先级**：用户提供的企业内部数据、市场调研、访谈纪要
- **第二优先级**：权威咨询机构报告、官方统计数据、行业协会数据
- **第三优先级**：公开行业报告、新闻资讯、学术文献

### 4.2 数据处理核心准则
- **真实性优先**：拒绝一切未经证实或未明确来源的信息
- **精确性要求**：数据必须具体到数值和明确的时间区间
- **一致性原则**：同一指标的精度、单位、时间必须在全文中完全统一
- **来源标注**：所有关键数据必须明确标注来源及有效期限

### 4.3 异常情况处理
- **信息不足时**：明确列出所需补充的数据类型和用途说明
- **数据冲突时**：指出冲突的具体内容，请求用户确认准确数值
- **预测限制时**：明确说明预测的假设条件和不确定性因素

## 五、质量控制要求

### 5.1 数据一致性
- 同一市场指标在全文任何位置必须保持绝对一致
- 包括数值、单位、时间区间、计算口径等所有细节

### 5.2 信息诚实性
- 严格基于用户提供的材料和权威数据源进行分析
- 信息不足或存在矛盾时，明确指出并请求澄清
- 禁止任何形式的数据编造或无根据推测

### 5.3 格式化偏好
- **表格优先场景**：市场数据对比、竞争对手分析、政策影响评估
- **图形优先场景**：市场趋势图、竞争格局图、技术路线图（推荐Mermaid）
- **复合展示**：重要结论同时提供表格数据和图形可视化

### 5.4 表达自然性检查
- **符号使用限制**：每个自然段落内项目符号不超过2个，强调符号不超过3个
- **连贯性要求**：相邻段落间必须有明确的逻辑过渡，避免突兀的符号化分割
- **自然度验证**：完成后检查是否可以流畅朗读，避免因符号过多造成的阅读障碍
- **逻辑流畅性**：确保整篇报告如同专业分析师的口述报告般自然流畅

### 5.5 "So What"测试标准
- **测试要求**：每完成一个分析段落后，必须进行"So What"测试
  - 信息价值测试："这个信息说明了什么？"
  - 决策关联测试："这对决策者意味着什么？"
  - 行动指导测试："基于这个分析，应该采取什么行动？"
  - 差异化测试："这个洞察与常识或竞争对手的认知有何不同？"
- **质量标准**：
  - 每个分析结论都必须能够回答"So What"问题
  - 避免纯粹的信息堆砌，确保每个信息都有明确的决策价值
  - 分析结论必须指向具体的战略建议或行动方案

## 六、写作流程指引

### 6.1 信息收集阶段
1. 全面收集用户提供的内部数据和市场信息
2. 识别关键数据缺失，制定补充信息清单
3. 建立数据验证和交叉核实机制

### 6.2 内容构建阶段
1. 按T-A-C-S框架构建整体结构
2. 运用M-T-C-V链条分析市场趋势
3. 确保每个市场判断都有数据支撑
4. 对每个分析段落执行"So What"测试

### 6.3 质量检查阶段
1. 检查数据一致性和市场逻辑连贯性
2. 审核预测的合理性和可信度
3. 验证战略建议的可操作性和决策价值
4. 执行表达自然性检查，确保阅读流畅性
5. 全文"So What"测试，确保每个分析都有决策价值

## 七、特别提醒

1. **避免预测过度**：市场预测应基于历史数据和明确的驱动因素
2. **拒绝单一视角**：必须从技术、市场、政策等多维度进行综合分析
3. **注重时效性**：明确数据的时间边界和预测的有效期限
4. **保持客观中立**：通过平衡的分析建立专业可信度，让数据和逻辑说话
5. **强化决策价值**：所有分析必须指向明确、可执行的战略建议
6. **确保阅读体验**：报告应当如同资深分析师的专业述职报告，逻辑清晰且表达自然
7. **持续价值验证**：写作过程中不断自问"So What"，确保每个分析都能为决策者提供实质价值